#!/usr/bin/env python3
"""
简单的流式翻译测试脚本
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_stream_translation():
    """测试流式翻译功能"""
    try:
        from app.services.translation_service import TranslationService, TranslationRequest, TranslationProvider
        
        print("=== 流式翻译测试 ===")
        
        # 创建翻译服务
        service = TranslationService()
        
        # 创建测试请求
        request = TranslationRequest(
            question=[{"text": "Hello, how are you today?"}],
            stream=True,
            translate_options={"src_lang": "en", "tgt_lang": "zh"},
            provider=TranslationProvider.LLM_TRANSLATE
        )
        
        print(f"测试请求: {request.question[0]['text']}")
        print("开始流式翻译...")
        
        # 执行流式翻译
        chunks = []
        translate_chunks = []
        
        async for chunk in service.stream_translate(request):
            chunks.append(chunk)
            print(f"收到chunk: {chunk}")
            
            # 解析翻译内容
            if chunk.startswith("data: ") and chunk.strip() != "data: [DONE]":
                try:
                    json_data = chunk[6:].strip()  # 移除 "data: " 前缀
                    parsed = json.loads(json_data)
                    
                    if "choices" in parsed and parsed["choices"]:
                        choice = parsed["choices"][0]
                        if "delta" in choice and "translateText" in choice["delta"]:
                            translate_text = choice["delta"]["translateText"]
                            translate_chunks.append(translate_text)
                            print(f"  -> 翻译片段: '{translate_text}'")
                            
                except (json.JSONDecodeError, KeyError) as e:
                    print(f"  -> 解析失败: {e}")
            
            # 限制输出数量以避免过多输出
            if len(chunks) >= 10:
                print("达到最大chunk数量，停止测试")
                break
        
        print(f"\n=== 测试结果 ===")
        print(f"总chunk数: {len(chunks)}")
        print(f"翻译片段数: {len(translate_chunks)}")
        print(f"完整翻译: {''.join(translate_chunks)}")
        
        # 验证结果
        if len(chunks) > 0:
            print("✅ 流式翻译测试成功")
            return True
        else:
            print("❌ 流式翻译测试失败：没有收到数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_translation_agent():
    """测试翻译智能体"""
    try:
        from app.agents.translation.translation_agent import TranslationAgent
        from app.core.models import AgentType
        
        print("\n=== 翻译智能体测试 ===")
        
        agent = TranslationAgent(AgentType.TRANSLATION)
        
        # 测试流式翻译
        result = await agent.process_translation(
            question=[{"text": "Good morning!"}],
            stream=True,
            translate_options={"src_lang": "en", "tgt_lang": "zh"},
            provider="llm_translate"
        )
        
        print("开始智能体流式翻译...")
        chunks = []
        
        async for chunk in result:
            chunks.append(chunk)
            print(f"智能体chunk: {chunk}")
            
            if len(chunks) >= 5:
                break
        
        print(f"智能体测试完成，收到 {len(chunks)} 个chunks")
        
        if len(chunks) > 0:
            print("✅ 翻译智能体测试成功")
            return True
        else:
            print("❌ 翻译智能体测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 智能体测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("开始流式翻译功能测试...\n")
    
    # 测试翻译服务
    service_result = await test_stream_translation()
    
    # 测试翻译智能体
    agent_result = await test_translation_agent()
    
    print(f"\n=== 最终结果 ===")
    print(f"翻译服务测试: {'✅ 通过' if service_result else '❌ 失败'}")
    print(f"翻译智能体测试: {'✅ 通过' if agent_result else '❌ 失败'}")
    
    if service_result and agent_result:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
