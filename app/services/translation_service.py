"""
翻译服务 - 支持多种翻译API提供商
"""
import aiohttp
import json
from typing import Dict, Any, Optional, List, AsyncGenerator
from dataclasses import dataclass
from enum import Enum

from app.config import settings
from app.services.llm_service import llm_service
from app.agents.translation.prompts import build_translation_messages
from app.utils.logger import get_logger

logger = get_logger(__name__)


class TranslationProvider(str, Enum):
    """翻译服务提供商"""
    DOUBAO = "doubao"
    LLM_TRANSLATE = "llm_translate"


@dataclass
class TranslationRequest:
    """翻译请求数据结构"""
    question: List[Dict[str, str]]  # [{"text": "hello"}, {"text": "world"}]
    stream: bool = False
    translate_options: Dict[str, str] = None  # {"src_lang": "en", "tgt_lang": "zh"}
    provider: str = "doubao"


@dataclass
class TranslationResult:
    """翻译结果数据结构"""
    code: str
    message: str
    data: List[Dict[str, str]]  # [{"text": "hello", "translateText": "你好"}]


class DoubaoTranslationClient:
    """Doubao翻译API客户端"""

    def __init__(self):
        self.api_url = settings.doubao_translation_api_url
        self.headers = {
            "appSysId": settings.doubao_app_sys_id,
            "token": settings.doubao_token,
            "Content-Type": "application/json"
        }
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    def _build_payload(self, request: TranslationRequest) -> Dict[str, Any]:
        """构建Doubao API请求载荷"""
        # 从question数组中提取所有text值
        text_list = [item["text"] for item in request.question]
        
        translate_options = request.translate_options or {}
        
        return {
            "data": {
                "text": text_list,
                "src_lang": translate_options.get("src_lang", "en"),
                "tgt_lang": translate_options.get("tgt_lang", "zh"),
                "model_name": "doubao"
            }
        }

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行Doubao翻译"""
        session = await self._get_session()
        payload = self._build_payload(request)
        
        logger.info(f"=== Doubao翻译请求调试信息 ===")
        logger.info(f"URL: {self.api_url}")
        logger.info(f"Headers: {self.headers}")
        logger.info(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            logger.info(f"开始执行Doubao翻译，文本数量: {len(request.question)}")
            
            async with session.post(
                self.api_url,
                headers=self.headers,
                json=payload
            ) as response:
                logger.info(f"=== Doubao翻译响应调试信息 ===")
                logger.info(f"状态码: {response.status}")
                logger.info(f"响应头: {dict(response.headers)}")
                
                response_text = await response.text()
                logger.info(f"响应内容: {response_text}")
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        logger.info(f"JSON解析成功，开始解析翻译结果...")
                        parsed_result = self._parse_doubao_result(result, request.question)
                        logger.info(f"Doubao翻译完成: code={parsed_result.code}")
                        return parsed_result
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"原始响应: {response_text}")
                        return TranslationResult(
                            code="error",
                            message=f"响应解析失败: {str(e)}",
                            data=[]
                        )
                else:
                    logger.error(f"Doubao翻译请求失败: {response.status}")
                    logger.error(f"错误内容: {response_text}")
                    return TranslationResult(
                        code="error",
                        message=f"请求失败: HTTP {response.status}",
                        data=[]
                    )
                    
        except Exception as e:
            logger.error(f"Doubao翻译执行异常: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"翻译异常: {str(e)}",
                data=[]
            )

    def _parse_doubao_result(self, response: Dict[str, Any], original_questions: List[Dict[str, str]]) -> TranslationResult:
        """解析Doubao API响应为标准格式"""
        logger.info(f"=== 开始解析Doubao翻译结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        try:
            # 根据实际API响应格式调整解析逻辑
            if "code" in response and response["code"] == 0:
                # 假设成功响应格式
                translated_texts = response.get("data", {}).get("translated_text", [])
                
                # 构建结果数据
                result_data = []
                for i, question in enumerate(original_questions):
                    translated_text = translated_texts[i] if i < len(translated_texts) else question["text"]
                    result_data.append({
                        "text": question["text"],
                        "translateText": translated_text
                    })
                
                return TranslationResult(
                    code="success",
                    message="翻译成功",
                    data=result_data
                )
            else:
                logger.warning(f"Doubao API返回错误: {response}")
                return TranslationResult(
                    code="error",
                    message=response.get("message", "翻译失败"),
                    data=[]
                )
                        
        except Exception as e:
            logger.error(f"解析Doubao翻译结果失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return TranslationResult(
                code="error",
                message=f"结果解析异常: {str(e)}",
                data=[]
            )

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


class LLMTranslationClient:
    """基于LLM的翻译客户端"""

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行LLM翻译"""
        try:
            translate_options = request.translate_options or {}
            src_lang = translate_options.get("src_lang", "en")
            tgt_lang = translate_options.get("tgt_lang", "zh")

            result_data = []

            # 逐个翻译每个文本
            for question_item in request.question:
                text = question_item["text"]

                # 构建翻译消息
                messages = build_translation_messages(text, src_lang, tgt_lang)

                # 调用LLM服务
                response = await llm_service.chat_completion(
                    messages=messages,
                    model=settings.default_llm_model,
                    max_tokens=len(text) * 3,  # 动态调整token数量
                    temperature=0.3
                )

                # 提取翻译结果
                translated_text = self._extract_translation(response)

                result_data.append({
                    "text": text,
                    "translateText": translated_text
                })

            return TranslationResult(
                code="success",
                message="翻译成功",
                data=result_data
            )

        except Exception as e:
            logger.error(f"LLM翻译失败: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"LLM翻译异常: {str(e)}",
                data=[]
            )

    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """执行流式LLM翻译 - 输出标准化的流式格式"""
        try:
            translate_options = request.translate_options or {}
            src_lang = translate_options.get("src_lang", "en")
            tgt_lang = translate_options.get("tgt_lang", "zh")

            # 为每个文本项创建翻译任务
            for text_index, question_item in enumerate(request.question):
                text = question_item["text"]

                # 构建翻译消息
                messages = build_translation_messages(text, src_lang, tgt_lang)

                # 调用LLM流式服务
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=settings.default_llm_model,
                    max_tokens=len(text) * 3,
                    temperature=0.3
                )

                # 处理流式响应并重新格式化为翻译格式
                translated_content = ""
                is_first_chunk = True

                async for chunk in stream_generator:
                    # 解析LLM流式响应
                    if chunk.startswith("data: "):
                        chunk_data = chunk[6:]  # 移除 "data: " 前缀

                        if chunk_data.strip() == "[DONE]":
                            # 当前文本翻译完成
                            break

                        try:
                            # 解析JSON数据
                            import json as json_lib
                            parsed_chunk = json_lib.loads(chunk_data)

                            # 提取增量内容
                            if "choices" in parsed_chunk and parsed_chunk["choices"]:
                                choice = parsed_chunk["choices"][0]
                                if "delta" in choice and "content" in choice["delta"]:
                                    delta_content = choice["delta"]["content"]
                                    translated_content += delta_content

                                    # 构建标准化的流式响应格式，包含翻译特定字段
                                    stream_response = {
                                        "choices": [{
                                            "delta": {
                                                "content": delta_content,
                                                "translateText": delta_content,  # 翻译特定字段
                                                "originalText": text if is_first_chunk else None,  # 原文只在第一个chunk中包含
                                                "role": "assistant" if is_first_chunk else None
                                            },
                                            "index": text_index
                                        }],
                                        "created": parsed_chunk.get("created"),
                                        "id": parsed_chunk.get("id"),
                                        "model": parsed_chunk.get("model", "translation-llm"),
                                        "object": "chat.completion.chunk"
                                    }

                                    # 清理None值
                                    if stream_response["choices"][0]["delta"]["originalText"] is None:
                                        del stream_response["choices"][0]["delta"]["originalText"]
                                    if stream_response["choices"][0]["delta"]["role"] is None:
                                        del stream_response["choices"][0]["delta"]["role"]

                                    yield f"data: {json.dumps(stream_response, ensure_ascii=False)}"
                                    is_first_chunk = False

                        except (json_lib.JSONDecodeError, KeyError) as e:
                            logger.warning(f"解析流式响应失败: {e}, chunk: {chunk_data}")
                            continue

            # 所有翻译完成
            yield "data: [DONE]"

        except Exception as e:
            logger.error(f"流式LLM翻译失败: {str(e)}", exc_info=True)
            error_response = {
                "choices": [{
                    "delta": {
                        "content": f"翻译错误: {str(e)}",
                        "role": "assistant"
                    },
                    "index": 0
                }],
                "error": True
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}"
            yield "data: [DONE]"

    def _extract_translation(self, response: Any) -> str:
        """从LLM响应中提取翻译文本"""
        try:
            if isinstance(response, dict):
                choices = response.get("choices", [])
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    if isinstance(content, str):
                        return content.strip()
            elif isinstance(response, str):
                return response.strip()
        except Exception as e:
            logger.error(f"提取翻译结果失败: {str(e)}")

        return ""


class TranslationService:
    """翻译服务管理器"""

    def __init__(self):
        self.doubao_client = DoubaoTranslationClient()
        self.llm_client = LLMTranslationClient()
        logger.info("初始化翻译服务")

    async def translate(self, request: TranslationRequest) -> TranslationResult:
        """执行翻译"""
        logger.info(f"=== TranslationService.translate 调用 ===")
        logger.info(f"Provider: {request.provider}")
        logger.info(f"文本数量: {len(request.question)}")
        logger.info(f"翻译选项: {request.translate_options}")
        
        try:
            if request.provider == TranslationProvider.DOUBAO:
                result = await self.doubao_client.translate(request)
            elif request.provider == TranslationProvider.LLM_TRANSLATE:
                result = await self.llm_client.translate(request)
            else:
                # 默认使用Doubao
                logger.warning(f"未知的翻译提供商: {request.provider}，使用默认的Doubao")
                result = await self.doubao_client.translate(request)
            
            logger.info(f"TranslationService.translate 返回结果: code={result.code}")
            return result
            
        except Exception as e:
            logger.error(f"翻译服务执行异常: {str(e)}", exc_info=True)
            return TranslationResult(
                code="error",
                message=f"翻译服务异常: {str(e)}",
                data=[]
            )

    async def stream_translate(self, request: TranslationRequest) -> AsyncGenerator[str, None]:
        """流式翻译 - 支持真正的实时翻译，输出标准化格式"""
        try:
            logger.info(f"开始流式翻译: provider={request.provider}, 文本数量={len(request.question)}")

            if request.provider == TranslationProvider.LLM_TRANSLATE:
                # 使用LLM客户端的流式翻译，直接透传标准化格式
                async for chunk in self.llm_client.stream_translate(request):
                    yield chunk
            elif request.provider == TranslationProvider.DOUBAO:
                # Doubao暂时不支持流式，使用包装方式转换为标准格式
                logger.info("Doubao不支持流式翻译，使用包装模式")
                result = await self.doubao_client.translate(request)

                # 将Doubao结果转换为标准流式格式
                if result.code == "success" and result.data:
                    for i, item in enumerate(result.data):
                        # 模拟流式输出，将翻译结果分块发送
                        translated_text = item.get("translateText", "")
                        original_text = item.get("text", "")

                        # 将翻译结果分成多个chunk
                        chunk_size = max(1, len(translated_text) // 3)
                        for j in range(0, len(translated_text), chunk_size):
                            chunk_content = translated_text[j:j + chunk_size]

                            stream_response = {
                                "choices": [{
                                    "delta": {
                                        "content": chunk_content,
                                        "translateText": chunk_content,
                                        "originalText": original_text if j == 0 else None,
                                        "role": "assistant" if j == 0 else None
                                    },
                                    "index": i
                                }],
                                "model": "doubao-translation",
                                "object": "chat.completion.chunk"
                            }

                            # 清理None值
                            if stream_response["choices"][0]["delta"]["originalText"] is None:
                                del stream_response["choices"][0]["delta"]["originalText"]
                            if stream_response["choices"][0]["delta"]["role"] is None:
                                del stream_response["choices"][0]["delta"]["role"]

                            yield f"data: {json.dumps(stream_response, ensure_ascii=False)}"
                else:
                    # 错误情况
                    error_response = {
                        "choices": [{
                            "delta": {
                                "content": f"翻译失败: {result.message}",
                                "role": "assistant"
                            },
                            "index": 0
                        }],
                        "error": True
                    }
                    yield f"data: {json.dumps(error_response, ensure_ascii=False)}"

                yield "data: [DONE]"
            else:
                # 默认使用LLM流式翻译
                logger.warning(f"未知的翻译提供商: {request.provider}，使用默认的LLM流式翻译")
                async for chunk in self.llm_client.stream_translate(request):
                    yield chunk

        except Exception as e:
            logger.error(f"流式翻译异常: {str(e)}", exc_info=True)
            error_response = {
                "choices": [{
                    "delta": {
                        "content": f"翻译异常: {str(e)}",
                        "role": "assistant"
                    },
                    "index": 0
                }],
                "error": True
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}"
            yield "data: [DONE]"

    async def close(self):
        """关闭服务"""
        await self.doubao_client.close()


# 全局翻译服务实例
translation_service = TranslationService()
