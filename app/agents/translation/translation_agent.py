"""
Translation agent with LangGraph workflow - supports multiple translation providers
"""
from typing import Op<PERSON>, AsyncGenerator, Union, Dict, Any, List
import json

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.translation_service import translation_service, TranslationRequest, TranslationProvider
from app.utils.logger import get_logger
from app.config import settings

logger = get_logger(__name__)


class TranslationAgent(BaseAgent):
    """Translation agent with multi-provider support"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="Translation Agent",
            description="Multi-provider translation agent with intelligent API selection"
        )

    def _build_graph(self):
        """Build LangGraph workflow for translation"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("detect_language", self._detect_language_node)
        workflow.add_node("select_provider", self._select_provider_node)
        workflow.add_node("translate_text", self._translate_text_node)
        workflow.add_node("validate_result", self._validate_result_node)
        workflow.add_node("fallback_translate", self._fallback_translate_node)
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        workflow.add_edge("validate_input", "detect_language")
        workflow.add_edge("detect_language", "select_provider")
        workflow.add_edge("select_provider", "translate_text")
        
        # Conditional routing after translation
        workflow.add_conditional_edges(
            "translate_text",
            self._route_after_translation,
            {
                "success": "finalize",
                "retry": "fallback_translate",
                "failed": "finalize"
            }
        )
        workflow.add_edge("fallback_translate", "finalize")
        workflow.add_edge("validate_result", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for translation agent")

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate translation input"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]
            
            # Validate required fields
            if "question" not in input_data or not input_data["question"]:
                raise ValueError("question field is required and cannot be empty")
            
            if not isinstance(input_data["question"], list):
                raise ValueError("question must be a list")
            
            for item in input_data["question"]:
                if not isinstance(item, dict) or "text" not in item:
                    raise ValueError("Each question item must be a dict with 'text' field")
            
            # Set defaults
            state["step_results"]["validated_input"] = {
                "question": input_data["question"],
                "stream": input_data.get("stream", False),
                "translate_options": input_data.get("translateOptions", {}),
                "provider": input_data.get("provider", "doubao")
            }
            
            logger.info(f"输入验证完成，文本数量: {len(input_data['question'])}")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state

    async def _detect_language_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Detect source language (simplified implementation)"""
        try:
            state["current_step"] = "detect_language"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            
            translate_options = validated_input["translate_options"]
            
            # Use provided language or default
            detected_lang = translate_options.get("src_lang", "en")
            target_lang = translate_options.get("tgt_lang", "zh")
            
            state["step_results"]["detected_lang"] = detected_lang
            state["step_results"]["target_lang"] = target_lang
            
            logger.info(f"语言检测完成: {detected_lang} -> {target_lang}")

        except Exception as e:
            state["error"] = f"Language detection failed: {str(e)}"
            logger.error(f"Language detection failed: {e}")

        return state

    async def _select_provider_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Select translation provider"""
        try:
            state["current_step"] = "select_provider"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            
            # Get provider from input or use default
            provider = validated_input.get("provider", "doubao")
            
            # Validate provider
            if provider not in [TranslationProvider.DOUBAO, TranslationProvider.LLM_TRANSLATE]:
                logger.warning(f"Unknown provider: {provider}, using default doubao")
                provider = TranslationProvider.DOUBAO
            
            state["step_results"]["selected_provider"] = provider
            logger.info(f"选择翻译提供商: {provider}")

        except Exception as e:
            state["error"] = f"Provider selection failed: {str(e)}"
            logger.error(f"Provider selection failed: {e}")

        return state

    async def _translate_text_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """执行翻译"""
        try:
            state["current_step"] = "translate_text"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            
            # 构建翻译请求
            translation_request = TranslationRequest(
                question=validated_input["question"],
                stream=validated_input["stream"],
                translate_options={
                    "src_lang": step_results["detected_lang"],
                    "tgt_lang": step_results["target_lang"]
                },
                provider=step_results["selected_provider"]
            )
            
            logger.info(f"开始翻译: provider={translation_request.provider}, stream={translation_request.stream}")
            
            # 根据是否流式处理调用不同的翻译服务方法
            if validated_input["stream"]:
                # 对于流式请求，我们只存储请求参数，实际流式处理在finalize阶段进行
                state["step_results"]["translation_request"] = translation_request
                state["step_results"]["is_stream"] = True
                logger.info("流式翻译请求已准备，将在finalize阶段处理")
                
                # 为了保持工作流程一致性，我们仍然需要一个结果对象
                # 这个结果只用于路由决策，不会返回给客户端
                state["step_results"]["translation_result"] = TranslationResult(
                    code="success",
                    message="流式翻译请求已准备",
                    data=[]
                )
            else:
                # 非流式请求直接调用translate方法
                result = await translation_service.translate(translation_request)
                state["step_results"]["translation_result"] = result
                logger.info(f"非流式翻译完成: code={result.code}, message={result.message}")
            
            state["step_results"]["attempt_count"] = state["step_results"].get("attempt_count", 0) + 1

        except Exception as e:
            state["error"] = f"翻译失败: {str(e)}"
            logger.error(f"翻译失败: {e}")

        return state

    def _route_after_translation(self, state: AgentWorkflowState) -> str:
        """Route after translation based on result"""
        step_results = state["step_results"]
        result = step_results.get("translation_result")
        attempt_count = step_results.get("attempt_count", 0)
        
        if not result or result.code != "success":
            if attempt_count < 2:  # Max 1 retry
                logger.info("翻译失败，尝试回退策略")
                return "retry"
            else:
                logger.warning("翻译失败，已达到最大重试次数")
                return "failed"
        
        return "success"

    async def _validate_result_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate translation result"""
        try:
            state["current_step"] = "validate_result"
            # Additional result validation logic can be added here
            logger.info("翻译结果验证完成")

        except Exception as e:
            state["error"] = f"Result validation failed: {str(e)}"
            logger.error(f"Result validation failed: {e}")

        return state

    async def _fallback_translate_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Fallback translation strategy"""
        try:
            state["current_step"] = "fallback_translate"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]
            current_provider = step_results["selected_provider"]
            
            # Switch to alternative provider
            fallback_provider = (TranslationProvider.LLM_TRANSLATE 
                                if current_provider == TranslationProvider.DOUBAO 
                                else TranslationProvider.DOUBAO)
            
            logger.info(f"使用回退翻译策略: {current_provider} -> {fallback_provider}")
            
            # Build fallback translation request
            translation_request = TranslationRequest(
                question=validated_input["question"],
                stream=validated_input["stream"],
                translate_options={
                    "src_lang": step_results["detected_lang"],
                    "tgt_lang": step_results["target_lang"]
                },
                provider=fallback_provider
            )
            
            # Call translation service with fallback provider
            result = await translation_service.translate(translation_request)
            
            state["step_results"]["translation_result"] = result
            state["step_results"]["used_fallback"] = True
            
            logger.info(f"回退翻译完成: code={result.code}")

        except Exception as e:
            state["error"] = f"Fallback translation failed: {str(e)}"
            logger.error(f"Fallback translation failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """完成翻译结果处理"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]
            
            # 检查是否为流式请求
            if step_results.get("is_stream", False):
                # 流式请求：存储翻译请求参数，供后续流式处理使用
                translation_request = step_results.get("translation_request")
                if translation_request:
                    # 将流式翻译生成器作为最终输出
                    state["final_output"] = translation_service.stream_translate(translation_request)
                    logger.info("流式翻译生成器已准备")
                else:
                    # 如果没有翻译请求，返回错误
                    error_response = {
                        "code": "error",
                        "message": "流式翻译请求缺失",
                        "data": []
                    }
                    state["final_output"] = error_response
                    logger.error("流式翻译请求缺失")
            else:
                # 非流式请求：直接返回翻译结果
                result = step_results.get("translation_result")
                if result:
                    state["final_output"] = {
                        "code": result.code,
                        "message": result.message,
                        "data": result.data
                    }
                else:
                    state["final_output"] = {
                        "code": "error",
                        "message": "翻译失败",
                        "data": []
                    }
                
                logger.info("非流式翻译流程完成")

        except Exception as e:
            state["error"] = f"结果处理失败: {str(e)}"
            logger.error(f"结果处理失败: {e}")

        return state

    # Required abstract method implementation
    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Process node logic (required by base class)"""
        return state

    # 直接访问方法用于API调用
    async def process_translation(self, 
                                question: List[Dict[str, str]],
                                stream: bool = False,
                                translate_options: Optional[Dict[str, str]] = None,
                                provider: str = "doubao") -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """翻译处理与直接结果传递"""
        
        # 准备LangGraph工作流的输入数据
        input_data = {
            "question": question,
            "stream": stream,
            "translateOptions": translate_options or {},
            "provider": provider
        }

        # 执行LangGraph工作流
        agent_state = await self.execute(input_data)
        
        if agent_state.status == TaskStatus.FAILED:
            # 返回适当格式的错误
            error_response = {
                "code": "error",
                "message": agent_state.error_message or "翻译失败",
                "data": []
            }
            
            if stream:
                # 流式错误响应
                async def error_stream():
                    yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                    yield "data: [DONE]\n\n"
                return error_stream()
            else:
                return error_response

        # 返回成功结果
        output_data = agent_state.output_data
        
        # 如果是流式请求且输出是生成器，直接返回生成器
        if stream and hasattr(output_data, "__aiter__"):
            return output_data
        elif stream:
            # 如果是流式请求但输出不是生成器，包装为流式响应
            async def success_stream():
                yield f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
            return success_stream()
        else:
            # 非流式请求直接返回结果
            return output_data
