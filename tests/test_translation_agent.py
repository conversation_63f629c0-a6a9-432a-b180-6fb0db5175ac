"""
翻译Agent集成测试
"""
import pytest
import asyncio
from app.agents.translation.translation_agent import TranslationAgent
from app.core.models import AgentType


@pytest.mark.asyncio
async def test_translation_agent_doubao():
    """测试Doubao翻译"""
    agent = TranslationAgent(AgentType.TRANSLATION)
    
    # 测试数据
    question = [
        {"text": "Hello"},
        {"text": "World"}
    ]
    
    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }
    
    # 执行翻译
    result = await agent.process_translation(
        question=question,
        stream=False,
        translate_options=translate_options,
        provider="doubao"
    )
    
    # 验证结果
    assert result is not None
    assert "code" in result
    assert "data" in result
    assert isinstance(result["data"], list)
    assert len(result["data"]) == 2


@pytest.mark.asyncio
async def test_translation_agent_llm():
    """测试LLM翻译"""
    agent = TranslationAgent(AgentType.TRANSLATION)
    
    # 测试数据
    question = [
        {"text": "Hello"},
        {"text": "World"}
    ]
    
    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }
    
    # 执行翻译
    result = await agent.process_translation(
        question=question,
        stream=False,
        translate_options=translate_options,
        provider="llm_translate"
    )
    
    # 验证结果
    assert result is not None
    assert "code" in result
    assert "data" in result
    assert isinstance(result["data"], list)
    assert len(result["data"]) == 2


@pytest.mark.asyncio
async def test_translation_agent_stream_llm():
    """测试LLM流式翻译"""
    agent = TranslationAgent(AgentType.TRANSLATION)

    # 测试数据
    question = [
        {"text": "Hello, how are you?"}
    ]

    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }

    # 执行流式翻译
    result = await agent.process_translation(
        question=question,
        stream=True,
        translate_options=translate_options,
        provider="llm_translate"
    )

    # 验证流式结果
    chunks = []
    translate_text_chunks = []
    has_original_text = False
    has_done_marker = False

    async for chunk in result:
        chunks.append(chunk)

        # 检查是否包含标准的流式格式
        if "data:" in chunk:
            if chunk.strip() == "data: [DONE]":
                has_done_marker = True
            else:
                try:
                    import json
                    # 提取JSON数据
                    json_data = chunk.replace("data: ", "").strip()
                    parsed_data = json.loads(json_data)

                    # 验证标准化格式
                    if "choices" in parsed_data:
                        choice = parsed_data["choices"][0]
                        if "delta" in choice:
                            delta = choice["delta"]

                            # 检查是否包含translateText字段
                            if "translateText" in delta:
                                translate_text_chunks.append(delta["translateText"])

                            # 检查是否包含原文（应该只在第一个chunk中）
                            if "originalText" in delta:
                                has_original_text = True
                                assert delta["originalText"] == "Hello, how are you?"

                except (json.JSONDecodeError, KeyError) as e:
                    # 忽略解析错误，继续测试
                    pass

    # 基本验证
    assert len(chunks) > 0, "应该有流式数据块"
    assert has_done_marker, "应该有[DONE]标记"
    assert len(translate_text_chunks) > 0, "应该有翻译文本块"
    assert has_original_text, "应该包含原文信息"

    # 验证翻译内容不为空
    full_translation = "".join(translate_text_chunks)
    assert len(full_translation.strip()) > 0, "翻译内容不应为空"


@pytest.mark.asyncio
async def test_translation_agent_stream_doubao():
    """测试Doubao流式翻译（包装模式）"""
    agent = TranslationAgent(AgentType.TRANSLATION)

    # 测试数据
    question = [
        {"text": "Hello"},
        {"text": "World"}
    ]

    translate_options = {
        "src_lang": "en",
        "tgt_lang": "zh"
    }

    # 执行流式翻译
    result = await agent.process_translation(
        question=question,
        stream=True,
        translate_options=translate_options,
        provider="doubao"
    )

    # 验证流式结果
    chunks = []
    translate_chunks = []
    has_done_marker = False

    async for chunk in result:
        chunks.append(chunk)

        if "data:" in chunk:
            if chunk.strip() == "data: [DONE]":
                has_done_marker = True
            else:
                try:
                    import json
                    json_data = chunk.replace("data: ", "").strip()
                    parsed_data = json.loads(json_data)

                    # 验证标准化格式
                    if "choices" in parsed_data:
                        choice = parsed_data["choices"][0]
                        if "delta" in choice and "translateText" in choice["delta"]:
                            translate_chunks.append(choice["delta"]["translateText"])

                except (json.JSONDecodeError, KeyError):
                    pass

    # 验证结果
    assert len(chunks) > 0, "应该有流式数据块"
    assert has_done_marker, "应该有[DONE]标记"
    assert len(translate_chunks) > 0, "应该有翻译文本块"


@pytest.mark.asyncio
async def test_translation_stream_format_consistency():
    """测试流式翻译输出格式的一致性"""
    agent = TranslationAgent(AgentType.TRANSLATION)

    question = [{"text": "Test message"}]
    translate_options = {"src_lang": "en", "tgt_lang": "zh"}

    # 测试LLM流式翻译格式
    result = await agent.process_translation(
        question=question,
        stream=True,
        translate_options=translate_options,
        provider="llm_translate"
    )

    standard_format_found = False
    async for chunk in result:
        if "data:" in chunk and chunk.strip() != "data: [DONE]":
            try:
                import json
                json_data = chunk.replace("data: ", "").strip()
                parsed_data = json.loads(json_data)

                # 验证标准格式结构
                if "choices" in parsed_data:
                    choice = parsed_data["choices"][0]
                    if "delta" in choice:
                        delta = choice["delta"]

                        # 验证必需字段
                        assert "content" in delta or "translateText" in delta, "应该包含content或translateText字段"

                        # 验证翻译特定字段
                        if "translateText" in delta:
                            standard_format_found = True
                            assert isinstance(delta["translateText"], str), "translateText应该是字符串"

                        # 验证索引字段
                        assert "index" in choice, "应该包含index字段"
                        assert isinstance(choice["index"], int), "index应该是整数"

            except (json.JSONDecodeError, KeyError, AssertionError) as e:
                print(f"格式验证失败: {e}, chunk: {chunk}")
                continue

    assert standard_format_found, "应该找到标准化的翻译格式"


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_translation_agent_llm())
